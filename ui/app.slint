import { Container } from "app/container.slint";
import { Player } from "app/player.slint";
export component App inherits Window {

    callback drag_window(x: length, y: length);
    default-font-size: 14px;

    min-width: 1150px;
    min-height: 720px;
    background: transparent;
    no-frame: true;

    fs := FocusScope { }

    TouchArea {
        moved => {
            root.drag_window(self.mouse-x - self.pressed-x, self.mouse-y - self.pressed-y);
        }
        clicked => {
            fs.focus();
        }
    }

    window := Rectangle {
        border-radius: 10px;
        VerticalLayout {
            container := Container {
                vertical-stretch: 1;
            }

            player := Player {
                height: 75px;
            }
        }
    }
}
