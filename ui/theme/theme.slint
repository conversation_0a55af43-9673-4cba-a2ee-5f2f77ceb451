import { ThemeOption } from "theme-option.slint";
import { ThemeStore, ThemeType } from "../app/global.slint";
export component Theme inherits Rectangle {

    VerticalLayout {
        alignment: start;
        spacing: 20px;

        free-theme := Rectangle {
            VerticalLayout {
                alignment: start;
                spacing: 10px;
                theme-name := Text {
                    text: "免费主题";
                    color: #8a8a8a;
                }

                theme-options := HorizontalLayout {
                    spacing: 20px;

                    ThemeOption {
                        theme: ThemeType.DARK;
                        background: black;
                        clicked => {
                            ThemeStore.current-theme = self.theme;
                        }
                    }

                    ThemeOption {
                        theme: ThemeType.ORANGE;
                        background: orange;
                        clicked => {
                            ThemeStore.current-theme = self.theme;
                        }
                    }

                    ThemeOption {
                        theme: ThemeType.PINK;
                        background: pink;
                        clicked => {
                            ThemeStore.current-theme = self.theme;
                            ThemeStore.sider-background = @linear-gradient(180deg, #f0c6c6 0%, #f5d6d6 50%, #eeeeee 100%);
                            ThemeStore.main-box-background = @linear-gradient(180deg, #fae8e8 0%, #f7eeee 50%, #f3f2f2 100%);
                            ThemeStore.nav-button-background = #eb7272;
                        }
                    }

                    ThemeOption {
                        theme: ThemeType.PURPLE;
                        background: purple;
                        clicked => {
                            ThemeStore.current-theme = self.theme;
                        }
                    }
                }
            }
        }
    }
}
