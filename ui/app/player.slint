export component Player inherits Rectangle {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background: #FAFAFA;

    VerticalLayout {
        alignment: start;

        line := Rectangle {
            height: 1px;
            background: #8a8a8a;
        }

        HorizontalLayout {
            padding: 10px;

            Rectangle {
                //height: root.height - 21px;
                background: black;
            }
        }
    }
}
