import { ToolBar } from "toolbar.slint";
import { ContentBox } from "content-box.slint";
import { ThemeStore } from "global.slint";
export component MainBox inherits Rectangle {
    border-top-right-radius: 10px;
    background: ThemeStore.main-box-background;

    VerticalLayout {
        padding-top: 20px;
        padding-left: 20px;
        padding-right: 20px;
        padding-bottom: 20px;
        toolbar := ToolBar {
            height: 30px;
        }

        content-box := ContentBox {
            vertical-stretch: 1;
        }
    }
}
