import { Icon } from "../components/icon.slint";
import { SearchInput } from "../components/search-input.slint";
import { NavStore, PageType } from "global.slint";
export component ToolBar inherits Rectangle {
    HorizontalLayout {
        alignment: space-between;

        left-box := Rectangle {
            HorizontalLayout {
                alignment: start;
                spacing: 20px;

                Icon {
                    source: @image-url("../../assets/arrow.svg");
                }

                Icon {
                    source: @image-url("../../assets/refresh.svg");
                }

                SearchInput {
                    placeholder: "请输入歌手或歌名";
                    search(text) => {
                        debug(text);
                    }
                }
            }
        }

        right-box := Rectangle {
            HorizontalLayout {
                alignment: space-between;
                spacing: 20px;
                Icon {
                    source: @image-url("../../assets/clothes.svg");
                    cliked => {
                        NavStore.current-page = PageType.THEME;
                    }
                }

                Icon {
                    source: @image-url("../../assets/minus.svg");
                }

                Icon {
                    source: @image-url("../../assets/maximize.svg");
                }

                Icon {
                    source: @image-url("../../assets/close.svg");
                }
            }
        }
    }
}
