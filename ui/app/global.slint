export enum PageType {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>AT<PERSON>,
    <PERSON>USIC-HOUSE,
    LIKE,
    REC<PERSON>TLY,
    <PERSON>OWNLOA<PERSON>,
    SINGER,
    THEME
}

export enum ThemeType {
    DEFAULT,
    DARK,
    ORAN<PERSON>,
    PURPLE,
    PINK
}

export global NavStore {
    in-out property <PageType> current-page;
}

export global ThemeStore {  
    in-out property <ThemeType> current-theme;
    in-out property <brush> sider-background: #eeeff0;
    in-out property <brush> main-box-background: #ffffff;
    in-out property <brush> nav-button-background: #dddddd;
}