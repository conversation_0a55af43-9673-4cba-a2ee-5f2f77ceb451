import { NavButton } from "../components/nav-button.slint";
import { NavStore, PageType, ThemeStore } from "global.slint";
export component SideBar inherits Rectangle {
    width: 210px;
    border-top-left-radius: 10px;
    background: ThemeStore.sider-background;

    VerticalLayout {
        alignment: start;
        padding-top: 25px;
        padding-left: 20px;
        padding-right: 20px;
        spacing: 20px;

        logo := Rectangle {
            height: 30px;

            HorizontalLayout {
                alignment: start;
                spacing: 5px;
                logo-pic := Rectangle {
                    width: logo.height;
                    border-radius: 5px;
                    clip: true;

                    Image {
                        height: parent.height;
                        width: self.height;
                        source: @image-url("../../assets/logo.svg");
                    }
                }

                Text {
                    text: "YoYo Music";
                    font-size: 20px;
                    vertical-alignment: center;
                    color: #000;
                }
            }
        }

        app-nav := VerticalLayout {
            alignment: start;
            spacing: 3px;
            NavButton {
                page-type: PageType.HOME;
                text: "推荐";
                icon: @image-url("../../assets/home.svg");
                clicked => {
                    NavStore.current-page = self.page-type;
                }
            }

            NavButton {
                page-type: PageType.PICKED;
                text: "精选";
                icon: @image-url("../../assets/star.svg");
                clicked => {
                    NavStore.current-page = self.page-type;
                }
            }

            NavButton {
                page-type: PageType.STATE;
                text: "动态";
                icon: @image-url("../../assets/chat.svg");
                clicked => {
                    NavStore.current-page = self.page-type;
                }
            }

            NavButton {
                page-type: PageType.MUSIC-HOUSE;
                text: "乐馆";
                icon: @image-url("../../assets/music.svg");
                clicked => {
                    NavStore.current-page = self.page-type;
                }
            }
        }

        line := Rectangle {
            height: 1px;
            background: #e4e8ec;
        }

        VerticalLayout {
            spacing: 3px;
            alignment: start;

            for item in [
                {
                    page-type: PageType.LIKE,
                    text: "我的点赞",
                    icon: @image-url("../../assets/like.svg")
                },
                {
                    page-type: PageType.RECENTLY,
                    text: "最近播放",
                    icon :@image-url("../../assets/time.svg")
                },
                {
                    page-type: PageType.DOWNLOAD,
                    text: "本地下载",
                    icon:@image-url("../../assets/download.svg")
                }
            ]: NavButton {
                page-type: item.page-type;
                text: item.text;
                icon: item.icon;
                clicked => {
                    NavStore.current-page = self.page-type;
                }
            }
        }
    }
}
