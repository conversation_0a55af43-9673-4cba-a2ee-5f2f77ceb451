import { Singer } from "../pages/singer/singer.slint";
import { Download } from "../pages/download/download.slint";
import { Recently } from "../pages/recently/recently.slint";
import { Like } from "../pages/like/like.slint";
import { Picked } from "../pages/picked/picked.slint";
import { State } from "../pages/state/State.slint";
import { MusicHouse } from "../pages/music-house/music-house.slint";
import { Home } from "../pages/home/<USER>";
import { NavStore, PageType } from "global.slint";
import { Theme } from "../theme/theme.slint";
export component ContentBox inherits Rectangle {
    VerticalLayout {
        padding-top: 20px;

        if NavStore.current-page == PageType.HOME: Home { }

        if NavStore.current-page == PageType.PICKED: Picked { }

        if NavStore.current-page == PageType.STATE: State { }

        if NavStore.current-page == PageType.MUSIC-HOUSE: MusicHouse { }

        if NavStore.current-page == PageType.LIKE: Like { }

        if NavStore.current-page == PageType.RECENTLY: Recently { }

        if NavStore.current-page == PageType.DOWNLOAD: Download { }
        if NavStore.current-page == PageType.SINGER: Singer { }
        if NavStore.current-page == PageType.THEME: Theme { }
    }
}
