import { <PERSON><PERSON>View } from "std-widgets.slint";
import { Icon } from "../../components/icon.slint";
import { SongImage } from "../../components/song-image.slint";
import { SingerTabs } from "singer-tabs.slint";

export component <PERSON> inherits Rectangle {
    ScrollView {
        VerticalLayout {
            spacing: 20px;
            person := Rectangle {
                height: 160px;
                HorizontalLayout {
                    spacing: 20px;
                    avatar := Rectangle {
                        height: person.height;
                        width: self.height;
                        border-radius: self.width / 2;
                        clip: true;

                        Image {
                            width: parent.width;
                            height: parent.height;
                            source: @image-url("../../../assets/jay.jpg");
                        }
                    }

                    info := Rectangle {
                        VerticalLayout {
                            alignment: space-between;
                            info-name := VerticalLayout {
                                spacing: 10px;
                                Text {
                                    text: "周杰伦";
                                    font-size: 30px;
                                    color: #000;
                                }

                                Text {
                                    text: "最强制作人";
                                    color: #000;
                                }

                                Text {
                                    text: "共4334万粉丝";
                                    color: gray;
                                }
                            }

                            btn := Rectangle {
                                width: 90px;
                                height: 34px;
                                border-radius: 5px;
                                background: red;

                                Text {
                                    text: "关注";
                                }

                                TouchArea {
                                    mouse-cursor: pointer;
                                    clicked => {
                                    }
                                }
                            }
                        }
                    }
                }
            }

            tabs := SingerTabs { }

            songs := Rectangle {
                layout := VerticalLayout {
                    property <int> current-index;
                    for item in 15: song-item := Rectangle {
                        height: 70px;
                        border-radius: 5px;
                        HorizontalLayout {
                            alignment: space-between;
                            padding-left: 15px;
                            padding-right: 15px;
                            padding-top: 5px;
                            padding-bottom: 5px;

                            left-box := Rectangle {
                                HorizontalLayout {
                                    alignment: start;
                                    play-icon := Icon {
                                        source: @image-url("../../../assets/play.svg");
                                        cliked => {
                                        }
                                    }

                                    SongImage {
                                        width: parent.height;
                                        height: self.width;
                                        source: @image-url("../../../assets/jay.jpg");
                                    }

                                    Text {
                                        text: "周杰伦-晴天";
                                        color: #8a8a8a;
                                        vertical-alignment: center;
                                    }
                                }
                            }

                            right-box := Rectangle {
                                HorizontalLayout {
                                    spacing: 200px;

                                    Text {
                                        text: "七里香";
                                        vertical-alignment: center;
                                        color: #8a8a8a;
                                    }

                                    Rectangle {
                                        HorizontalLayout {
                                            spacing: 20px;
                                            Icon {
                                                source: @image-url("../../../assets/thumbsup.svg");
                                            }

                                            Text {
                                                text: "04:30";
                                                vertical-alignment: center;
                                                color: #8a8a8a;
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        states [
                            active when layout.current-index == item: {
                                song-item.background: #eee;
                            }
                        ]

                        TouchArea {
                            mouse-cursor: pointer;
                            clicked => {
                                layout.current-index = item;
                            }
                        }
                    }
                }
            }
        }
    }
}
