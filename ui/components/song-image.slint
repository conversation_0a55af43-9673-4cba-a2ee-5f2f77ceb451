export component SongImage inherits Rectangle {
    in property <image> source;
    callback cliked();

    HorizontalLayout {
        alignment: center;
        VerticalLayout {
            alignment: center;

            Rectangle {
                width: root.height / 2;
                height: self.width;
                border-radius: 5px;
                clip: true;
                Image {
                    width: root.height;
                    height: self.width;
                    source: root.source;
                }
            }
        }
    }

    TouchArea {
        mouse-cursor: pointer;
        clicked => {
            root.cliked();
        }
    }
}
