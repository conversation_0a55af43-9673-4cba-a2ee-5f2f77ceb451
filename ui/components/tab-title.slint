export component TabTitle inherits Rectangle {
    in-out property <bool> is-active;
    in property <int> index;
    in property <string> text;
    callback clicked();

    VerticalLayout {
        alignment: space-between;
        title := Text {
            text: "歌曲";
            color: #000;
        }

        if root.is-active: Rectangle {
            x: (title.width - self.width) / 2;
            width: 20px;
            height: 2px;
            background: red;
        }
    }

    TouchArea {
        mouse-cursor: pointer;
        clicked => {
            root.clicked();
        }
    }
}
