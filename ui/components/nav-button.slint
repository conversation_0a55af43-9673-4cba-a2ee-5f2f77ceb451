import { NavStore, PageType, ThemeStore } from "../app/global.slint";
export component NavButton inherits Rectangle {
    in property <PageType> page-type;
    in property <string> text;
    in property <image> icon;
    callback clicked();

    width: 170px;
    height: 36px;
    border-radius: 5px;

    HorizontalLayout {
        padding-left: 10px;
        padding-right: 10px;
        spacing: 5px;
        alignment: start;

        VerticalLayout {
            alignment: center;
            icon := Image {
                height: root.height / 2;
                width: self.height;
                source: root.icon;
            }
        }

        nav-name := Text {
            text: root.text;
            font-size: 14px;
            vertical-alignment: center;
            color: #8a8a8a;
        }
    }

    states [
        active when NavStore.current-page == root.page-type: {
            root.background: ThemeStore.nav-button-background;
            icon.colorize: #000;
            nav-name.color: #000;
        }
    ]

    TouchArea {
        mouse-cursor: pointer;
        clicked => {
            root.clicked();
        }
    }
}
