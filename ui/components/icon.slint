export component Icon inherits Rectangle {
    in property <image> source;
    in-out property <brush> colorize;
    callback cliked();

    width: 14px;

    HorizontalLayout {
        alignment: center;
        VerticalLayout {
            alignment: center;
            Image {
                width: root.width;
                height: self.width;
                source: root.source;
                colorize: root.colorize;
            }
        }
    }

    TouchArea {
        mouse-cursor: pointer;
        clicked => {
            root.cliked();
        }
    }
}
