import { Icon } from "icon.slint";
export component SearchInput inherits Rectangle {
    in property <string> placeholder;
    in property <InputType> input-type: text;
    callback search(text: string);

    width: 200px;
    border-width: 1px;
    border-radius: 5px;
    border-color: #8a8a8a;
    clip: true;
    background: #fff;

    VerticalLayout {
        alignment: center;
        padding-left: 5px;
//        padding-right: 5px;

        HorizontalLayout {
            alignment: start;
            spacing: 5px;
            icon := Icon {
                source: @image-url("../../assets/search.svg");
            }

            text-input := TextInput {
                padding-right: 5px;
                horizontal-stretch: 1;
                vertical-alignment: center;
                text: root.placeholder;
                input-type: root.input-type;
                single-line: true;
                color: #8a8a8a;
                font-size: 12px;

                changed has-focus => {
                    if self.has-focus {
                        if self.text == root.placeholder {
                            self.text = "";
                        }
                    } else {
                        if self.text == "" {
                            self.text = root.placeholder;
                        }
                    }
                }

                accepted => {
                    if self.text != "" {
                        root.search(self.text);
                    }
                }
            }
        }
    }

    TouchArea {
        mouse-cursor: pointer;
        clicked => {
            text-input.focus();
        }
    }
}
